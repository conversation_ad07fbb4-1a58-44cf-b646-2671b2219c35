<template>
  <section class="service-rate-table-read-only">
    <v-flex class="button-group">
      <v-tooltip
        v-for="rateType in availableRateTypes"
        :key="rateType.rateTypeId"
        bottom
        :disabled="true"
      >
        <template v-slot:activator="{ on }">
          <span v-on="on">
            <v-btn
              :class="{
                'v-btn--active': selectedRateTypeId === rateType.rateTypeId,
              }"
              flat
              @click="selectedRateTypeId = rateType.rateTypeId"
            >
              <span class="px-2">
                <strong>{{ rateType.longName.toUpperCase() }} RATES</strong>
              </span>
            </v-btn>
          </span>
        </template>
      </v-tooltip>
    </v-flex>
    <!-- No Data State -->
    <v-alert
      v-if="!serviceRate || !serviceRate.rateTableItems.length"
      :value="true"
      color="info"
      icon="info"
      class="mb-3"
    >
      No service rates are currently configured.
    </v-alert>

    <!-- Time Rate Table -->
    <div
      v-if="selectedRateTypeId === JobRateType.TIME && timeRateItems.length > 0"
    >
      <v-data-table
        class="default-table-dark client-invoice-accounting-table gd-dark-theme"
        :headers="timeHeaders"
        :items="timeRateItems"
        hide-actions
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="slotProps">
          <tr>
            <td>{{ getServiceName(slotProps.item.serviceTypeId) }}</td>
            <td>
              <v-tooltip bottom :color="getRateVariationColor(slotProps.item)">
                <template v-slot:activator="{ on, attrs }">
                  <span
                    v-bind="attrs"
                    v-on="on"
                    class="rate-text"
                    :class="getRateVariationColor(slotProps.item)"
                  >
                    {{ formatTimeRateWithVariation(slotProps.item) }}
                  </span>
                </template>
                <span>{{ getTimeRateTooltip(slotProps.item) }}</span>
              </v-tooltip>
            </td>
            <td>{{ formatMinCharge(slotProps.item.rateTypeObject) }}</td>
            <td>{{ formatChargeIncrement(slotProps.item.rateTypeObject) }}</td>
            <td>{{ formatGrace(slotProps.item.rateTypeObject.graceType) }}</td>
            <td>
              <v-tooltip bottom :color="getRateVariationColor(slotProps.item)">
                <template v-slot:activator="{ on, attrs }">
                  <span
                    v-bind="attrs"
                    v-on="on"
                    class="rate-text"
                    :class="getRateVariationColor(slotProps.item)"
                  >
                    {{ formatStandbyRateWithVariation(slotProps.item) }}
                  </span>
                </template>
                <span>{{ getStandbyRateTooltip(slotProps.item) }}</span>
              </v-tooltip>
            </td>
            <td>
              {{
                formatFuelSurcharge(
                  slotProps.item.rateTypeObject.appliedFuelSurchargeId,
                )
              }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>

    <!-- Zone Rate Table -->
    <div
      v-if="selectedRateTypeId === JobRateType.ZONE && zoneRateItems.length > 0"
    >
      <v-data-table
        class="default-table-dark client-invoice-accounting-table gd-dark-theme"
        :headers="zoneHeaders"
        :items="zoneRateTableData"
        hide-actions
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="slotProps">
          <tr>
            <td>{{ slotProps.item.serviceTypeName }}</td>
            <td>
              <v-tooltip bottom :color="getRateVariationColor(slotProps.item)">
                <template v-slot:activator="{ on, attrs }">
                  <span
                    v-bind="attrs"
                    v-on="on"
                    class="rate-text"
                    :class="getRateVariationColor(slotProps.item)"
                  >
                    {{ slotProps.item.zoneRate }}
                  </span>
                </template>
                <span>{{ getZoneRateTooltip(slotProps.item) }}</span>
              </v-tooltip>
            </td>
            <td>{{ slotProps.item.pickupFlagfall }}</td>
            <td>{{ slotProps.item.dropoffFlagfall }}</td>
            <td>{{ slotProps.item.percentage }}</td>
            <td>{{ slotProps.item.demurrageRate }}</td>
            <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
          </tr>
        </template>
      </v-data-table>
    </div>

    <!-- Distance Rate Table -->
    <div
      v-if="
        selectedRateTypeId === JobRateType.DISTANCE &&
        distanceRateItems.length > 0
      "
    >
      <v-data-table
        class="default-table-dark client-invoice-accounting-table gd-dark-theme"
        :headers="distanceHeaders"
        :items="distanceRateItems"
        hide-actions
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="slotProps">
          <tr>
            <td>{{ getServiceName(slotProps.item.serviceTypeId) }}</td>
            <td>{{ formatBaseFreight(slotProps.item.rateTypeObject) }}</td>
            <td>{{ formatIncrement(slotProps.item.rateTypeObject) }}</td>
            <td>{{ formatMinimumCharge(slotProps.item.rateTypeObject) }}</td>
            <td>{{ formatRange(slotProps.item.rateTypeObject) }}</td>
            <td>
              {{
                formatFuelSurcharge(
                  slotProps.item.rateTypeObject.appliedFuelSurchargeId,
                )
              }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>

    <!-- Point-to-Point Rate Table -->
    <div
      v-if="
        selectedRateTypeId === JobRateType.POINT_TO_POINT &&
        pointToPointRateItems.length > 0
      "
    >
      <v-data-table
        class="default-table-dark client-invoice-accounting-table gd-dark-theme"
        :headers="pointToPointHeaders"
        :items="pointToPointRateTableData"
        hide-actions
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="slotProps">
          <tr>
            <td>{{ slotProps.item.serviceTypeName }}</td>
            <td>{{ slotProps.item.fromAddress }}</td>
            <td>{{ slotProps.item.toAddress }}</td>
            <td>
              <v-tooltip bottom :color="getRateVariationColor(slotProps.item)">
                <template v-slot:activator="{ on, attrs }">
                  <span
                    v-bind="attrs"
                    v-on="on"
                    class="rate-text"
                    :class="getRateVariationColor(slotProps.item)"
                  >
                    {{ slotProps.item.rate }}
                  </span>
                </template>
                <span>{{ getPointToPointRateTooltip(slotProps.item) }}</span>
              </v-tooltip>
            </td>
            <td>{{ slotProps.item.demurrageRate }}</td>
            <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
          </tr>
        </template>
      </v-data-table>
    </div>

    <!-- Unit Rate Table -->
    <div
      v-if="selectedRateTypeId === JobRateType.UNIT && unitRateItems.length > 0"
    >
      <v-data-table
        class="default-table-dark client-invoice-accounting-table gd-dark-theme"
        :headers="unitHeaders"
        :items="unitRateTableData"
        hide-actions
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="slotProps">
          <tr>
            <td>{{ slotProps.item.unitTypeName }}</td>
            <td>{{ slotProps.item.zoneName }}</td>
            <td>{{ slotProps.item.unitRate }}</td>
            <td>
              {{ slotProps.item.unitAmountMultiplier }}
            </td>
            <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
            <td>{{ slotProps.item.demurrageRate }}</td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </section>
</template>

<script setup lang="ts">
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import { returnGraceShortName } from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import { rateMultipliers } from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import serviceTypeRates, {
  JobRateType,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { returnReadableChargeBasisName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { returnReadableRateBracketTypeName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const props = withDefaults(
  defineProps<{
    clientId: string;
    searchDate?: number;
    showRateVariationsHighlight?: boolean;
  }>(),
  {
    searchDate: Date.now(),
    showRateVariationsHighlight: true,
  },
);

// Store initialization
const serviceRateStore = useServiceRateStore();
const serviceRateVariationsStore = useServiceRateVariationsStore();

const selectedRateTypeId: Ref<JobRateType> = ref(JobRateType.TIME);

const availableRateTypes: ComputedRef<ServiceTypeRates[]> = computed(() => {
  return serviceTypeRates.filter((rateType) => !rateType.adhoc);
});

// service rates and rate variations
const serviceRate: Ref<ClientServiceRate | null> = ref(null);
const isLoadingServiceRate: Ref<boolean> = ref(false);
const rateVariations: Ref<ClientServiceRateVariations[]> = ref([]);
const isLoadingVariations: Ref<boolean> = ref(false);

// Load service rates for the client
async function loadServiceRate(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoadingServiceRate.value = true;
  try {
    const searchDate = props.searchDate || Date.now();
    const response: CurrentClientServiceRateResponse | null =
      await serviceRateStore.getMergedClientServiceRates(
        props.clientId,
        searchDate,
      );
    if (response) {
      serviceRate.value = response.clientServiceRate;
    }
  } catch (error) {
    console.error('Error loading service rates:', error);
    serviceRate.value = null;
  } finally {
    isLoadingServiceRate.value = false;
  }
}

// Helper function to get service name
function getServiceName(serviceTypeId: number | undefined): string {
  if (!serviceTypeId) {
    return '-';
  }
  const serviceType = getServiceTypeById(serviceTypeId);
  return serviceType ? serviceType.optionSelectName : '-';
}

// Time Rate Items and Headers
const timeRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.TIME,
  );
});

const timeHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  { text: 'Min Charge', align: 'left', value: 'minCharge', sortable: false },
  {
    text: 'Charge Increment',
    align: 'left',
    value: 'chargeIncrement',
    sortable: false,
  },
  { text: 'Grace', align: 'left', value: 'grace', sortable: false },
  {
    text: 'Standby Rate',
    align: 'left',
    value: 'standbyRate',
    sortable: false,
  },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Zone Rate Items and Headers
const zoneRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.ZONE,
  );
});

const zoneHeaders: ComputedRef<TableHeader[]> = computed(() => {
  const headers: TableHeader[] = [
    {
      text: 'Service Type',
      align: 'left',
      value: 'serviceType',
      sortable: false,
    },
  ];

  headers.push(
    { text: 'Zone Rate', align: 'left', value: 'zoneRate', sortable: false },
    {
      text: 'Pickup Flag Fall',
      align: 'left',
      value: 'pickupFlagfall',
      sortable: false,
    },
    {
      text: 'Drop-off Flag Fall',
      align: 'left',
      value: 'dropoffFlagfall',
      sortable: false,
    },
  );
  headers.push({
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  });

  headers.push(
    { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
    {
      text: 'Fuel Surcharge',
      align: 'left',
      value: 'fuelSurcharge',
      sortable: false,
    },
  );

  return headers;
});

// Distance Rate Items and Headers
const distanceRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.DISTANCE,
  );
});

const distanceHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  {
    text: 'Base Freight Rate',
    align: 'left',
    value: 'baseFreight',
    sortable: false,
  },
  { text: 'Increment', align: 'left', value: 'increment', sortable: false },
  {
    text: 'Minimum Charge',
    align: 'left',
    value: 'minimumCharge',
    sortable: false,
  },
  { text: 'Range', align: 'left', value: 'range', sortable: false },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Point-to-Point Rate Items and Headers
const pointToPointRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.POINT_TO_POINT,
  );
});

const pointToPointHeaders: ComputedRef<TableHeader[]> = computed(() => {
  const headers: TableHeader[] = [
    {
      text: 'Service Type',
      align: 'left',
      value: 'serviceType',
      sortable: false,
    },
  ];

  headers.push(
    { text: 'From', align: 'left', value: 'from', sortable: false },
    { text: 'To', align: 'left', value: 'to', sortable: false },
    { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  );
  headers.push({
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  });

  headers.push(
    { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
    {
      text: 'Fuel Surcharge',
      align: 'left',
      value: 'fuelSurcharge',
      sortable: false,
    },
  );

  return headers;
});

// Unit Rate Items and Headers
const unitRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.UNIT,
  );
});

const unitHeaders: ComputedRef<TableHeader[]> = computed(() => {
  const headers: TableHeader[] = [
    {
      text: 'Service Type',
      align: 'left',
      value: 'serviceType',
      sortable: false,
    },
  ];

  headers.push(
    { text: 'Unit Type', align: 'left', value: 'unitType', sortable: false },
    { text: 'Zone', align: 'left', value: 'zone', sortable: false },
    { text: 'Rate', align: 'left', value: 'rate', sortable: false },
    {
      text: 'Per Amount',
      align: 'left',
      value: 'perAmount',
      sortable: false,
    },
  );
  headers.push({
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  });

  headers.push(
    {
      text: 'Fuel Surcharge',
      align: 'left',
      value: 'fuelSurcharge',
      sortable: false,
    },
    { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
  );

  return headers;
});

// Computed properties for table data
const zoneRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const tableData: any[] = [];

  for (const service of zoneRateItems.value) {
    const serviceTypeName = getServiceName(service.serviceTypeId);
    const zoneRates: ZoneRateType[] = service.rateTypeObject as ZoneRateType[];

    const zoneTableRate = zoneRates.map((zone: ZoneRateType, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => zone.appliedFuelSurchargeId === x.id,
      );

      // const multiplier = rateMultipliers.find(
      //   (rateMultiplier) =>
      //     rateMultiplier.id === zone.demurrage.incrementMultiplier,
      // );

      const demurrageGraceAsMinutes = moment
        .duration(zone.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      return {
        index,
        serviceTypeName,
        serviceTypeId: service.serviceTypeId,
        zoneName: zone.zoneName,
        zoneRate: formatZoneRateWithVariation(
          { serviceTypeId: service.serviceTypeId },
          zone.rate,
        ),
        baseZoneRate: zone.rate, // Store base rate for tooltip
        pickupFlagfall: `${zone.additionalPickUpFlagFall} mins`,
        dropoffFlagfall: `${zone.additionalDropOffFlagFall} mins`,
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          zone.demurrage.rate,
        )}/hr with grace period`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${zone.percentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: zone.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
      };
    });

    tableData.push(...zoneTableRate);
  }

  return tableData;
});

const pointToPointRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const tableData: any[] = [];

  for (const service of pointToPointRateItems.value) {
    const serviceTypeName = getServiceName(service.serviceTypeId);
    const p2pRates: PointToPointRateType[] =
      service.rateTypeObject as PointToPointRateType[];

    const p2pTableRate = p2pRates.map(
      (p2p: PointToPointRateType, index: number) => {
        const appliedFuelSurcharge = applicableFuelSurcharges.find(
          (x) => p2p.appliedFuelSurchargeId === x.id,
        );

        // const multiplier = rateMultipliers.find(
        //   (rateMultiplier) =>
        //     rateMultiplier.id === p2p.demurrage.incrementMultiplier,
        // );

        // const demurrageGraceAsMinutes = moment
        //   .duration(p2p.demurrage.graceTimeInMilliseconds)
        //   .asMinutes();

        return {
          index,
          serviceTypeName,
          serviceTypeId: service.serviceTypeId,
          fromAddress: p2p.fromAddressReference,
          toAddress: p2p.toAddressReference,
          rate: formatZoneRateWithVariation(
            { serviceTypeId: service.serviceTypeId },
            p2p.rate,
          ),
          baseRate: p2p.rate, // Store base rate for tooltip
          demurrageRate: `Apply: $${DisplayCurrencyValue(
            p2p.demurrage.rate,
          )}/hr with grace period`,
          appliedFuelSurcharge: appliedFuelSurcharge
            ? appliedFuelSurcharge.shortName
            : '-',
          percentage: `${p2p.percentage}%`,
          isClient: true,
          demurrageFuelSurchargeApplies: p2p.demurrage
            .demurrageFuelSurchargeApplies
            ? 'Apply'
            : "Don't Apply",
        };
      },
    );

    tableData.push(...p2pTableRate);
  }

  return tableData;
});

const unitRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const tableData: any[] = [];

  for (const service of unitRateItems.value) {
    const serviceTypeName = getServiceName(service.serviceTypeId);
    const unitRates: UnitRate[] = service.rateTypeObject as UnitRate[];

    const unitTableRate = unitRates.map((unit: UnitRate, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => unit.appliedFuelSurchargeId === x.id,
      );

      // const multiplier = rateMultipliers.find(
      //   (rateMultiplier) =>
      //     rateMultiplier.id === unit.demurrage.incrementMultiplier,
      // );

      return {
        index,
        serviceTypeName,
        serviceTypeId: service.serviceTypeId,
        unitTypeName: unit.unitTypeName,
        zoneName: unit.zoneName,
        unitRate: unit.unitRanges
          .map((range) => `$${DisplayCurrencyValue(range.unitRate)}`)
          .join(', '),
        unitAmountMultiplier: unit.unitRanges
          .map((range) => range.unitAmountMultiplier)
          .join(', '),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          unit.demurrage.rate,
        )}/hr with grace period`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${unit.fleetAssetPercentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: unit.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
      };
    });

    tableData.push(...unitTableRate);
  }

  return tableData;
});

// Formatting functions for Time Rate (kept for backward compatibility)
function formatMinCharge(rateObj: TimeRateType): string {
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.minChargeMultiplier,
  );
  return `${rateObj.minCharge} ${multiplier?.longName || 'mins'}`;
}

const formatChargeIncrement = (rateObj: TimeRateType): string => {
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.chargeIncrementMultiplier,
  );
  return `${rateObj.chargeIncrement} ${multiplier?.longName || 'mins'}`;
};

const formatGrace = (graceType: number): string => {
  return returnGraceShortName(graceType) || 'NR';
};

const formatFuelSurcharge = (fuelSurchargeId: number): string => {
  const fuelSurcharge = applicableFuelSurcharges.find(
    (f) => f.id === fuelSurchargeId,
  );
  return fuelSurcharge?.shortName || 'Apply';
};

// Helper function to get rate variation percentage for an item
function getRateVariationPercentage(item: any): number | null {
  if (!rateVariations.value.length) {
    return null;
  }

  const matchingVariation = rateVariations.value.find((variation) => {
    const serviceTypeMatches =
      variation.serviceTypeId === null ||
      variation.serviceTypeId === item.serviceTypeId;

    const rateTypeMatches =
      variation.rateTypeId === null ||
      variation.rateTypeId === selectedRateTypeId.value;

    const now = Date.now();
    const validFrom = variation.validFromDate || 0;
    const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;
    const isActive = now >= validFrom && now <= validTo;

    return serviceTypeMatches && rateTypeMatches && isActive;
  });

  if (!matchingVariation) {
    return null;
  }

  const clientAdjustment = matchingVariation.clientAdjustmentPercentage;
  const fleetAssetAdjustment = matchingVariation.fleetAssetAdjustmentPercentage;
  return clientAdjustment !== null ? clientAdjustment : fleetAssetAdjustment;
}

// Helper function to calculate adjusted rate
function calculateAdjustedRate(
  baseRate: number,
  variationPercentage: number | null,
): number {
  if (variationPercentage === null) {
    return baseRate;
  }
  return baseRate * (1 + variationPercentage / 100);
}

// Enhanced formatting functions with rate variations
function formatTimeRateWithVariation(item: any): string {
  const rateObj: TimeRateType = item.rateTypeObject;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.rateMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(rateObj.rate, variationPercentage);

  let result = `$${DisplayCurrencyValue(adjustedRate)}/${unit}`;

  return result;
}

function formatStandbyRateWithVariation(item: any): string {
  const rateObj: TimeRateType = item.rateTypeObject;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.standbyMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(
    rateObj.standbyRate,
    variationPercentage,
  );

  let result = `$${DisplayCurrencyValue(adjustedRate)}/${unit}`;

  return result;
}

// Enhanced formatting function for Zone Rate
function formatZoneRateWithVariation(item: any, baseRate: number): string {
  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);

  let result = `$${DisplayCurrencyValue(adjustedRate)}/hr`;

  return result;
}

// Tooltip functions for rate calculations
function getTimeRateTooltip(item: any): string {
  const rateObj: TimeRateType = item.rateTypeObject;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.rateMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);

  if (variationPercentage === null) {
    return `Base rate: $${DisplayCurrencyValue(rateObj.rate)}/${unit}`;
  }

  const adjustedRate = calculateAdjustedRate(rateObj.rate, variationPercentage);
  const sign = variationPercentage >= 0 ? '+' : '';
  return `$${DisplayCurrencyValue(
    rateObj.rate,
  )}/${unit} ${sign}${variationPercentage.toFixed(
    2,
  )}% = $${DisplayCurrencyValue(adjustedRate)}/${unit}`;
}

function getStandbyRateTooltip(item: any): string {
  const rateObj: TimeRateType = item.rateTypeObject;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.standbyMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);

  if (variationPercentage === null) {
    return `Base standby rate: $${DisplayCurrencyValue(
      rateObj.standbyRate,
    )}/${unit}`;
  }

  const adjustedRate = calculateAdjustedRate(
    rateObj.standbyRate,
    variationPercentage,
  );
  const sign = variationPercentage >= 0 ? '+' : '';
  return `$${DisplayCurrencyValue(
    rateObj.standbyRate,
  )}/${unit} ${sign}${variationPercentage.toFixed(
    2,
  )}% = $${DisplayCurrencyValue(adjustedRate)}/${unit}`;
}

function getZoneRateTooltip(item: any): string {
  const variationPercentage = getRateVariationPercentage(item);
  const baseRate = item.baseZoneRate;

  if (variationPercentage === null) {
    return `Base zone rate: $${DisplayCurrencyValue(baseRate)}/hr`;
  }

  const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);
  const sign = variationPercentage >= 0 ? '+' : '';
  return `$${DisplayCurrencyValue(
    baseRate,
  )}/hr ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
    adjustedRate,
  )}/hr`;
}

function getPointToPointRateTooltip(item: any): string {
  const variationPercentage = getRateVariationPercentage(item);
  const baseRate = item.baseRate;

  if (variationPercentage === null) {
    return `Base rate: $${DisplayCurrencyValue(baseRate)}/hr`;
  }

  const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);
  const sign = variationPercentage >= 0 ? '+' : '';
  return `$${DisplayCurrencyValue(
    baseRate,
  )}/hr ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
    adjustedRate,
  )}/hr`;
}

// Helper function to get tooltip background color based on rate variation
function getRateVariationColor(item: any): string {
  const variationPercentage = getRateVariationPercentage(item);

  if (variationPercentage === null || item.rate <= 0) {
    return 'neutral'; // Default grey for no variation
  }

  if (variationPercentage > 0) {
    return 'positive'; // Green for positive variation (rate increase)
  } else {
    return 'negative'; // Red for negative variation (rate decrease)
  }
}

// Formatting functions for Distance Rate
function formatBaseFreight(rateObj: DistanceRateType): string {
  return `$${DisplayCurrencyValue(rateObj.baseFreightCharge || 0)}`;
}

const formatIncrement = (rateObj: DistanceRateType): string => {
  return `${returnReadableChargeBasisName(
    rateObj.chargeBasis,
  )} (${returnReadableRateBracketTypeName(rateObj.rateBracketType)})`;
};

const formatMinimumCharge = (rateObj: DistanceRateType): string => {
  return `$${DisplayCurrencyValue(rateObj.minCharge || 0)}`;
};

const formatRange = (rateObj: DistanceRateType): string => {
  if (rateObj.rates && rateObj.rates.length > 0) {
    const firstRange = rateObj.rates[0];
    const lastRange = rateObj.rates[rateObj.rates.length - 1];
    return `${firstRange.bracketMin}km - ${
      lastRange.bracketMax === -1 ? '∞' : lastRange.bracketMax + 'km'
    }`;
  }
  return '-';
};

// Load rate variations for the client
async function loadRateVariations(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoadingVariations.value = true;
  try {
    const searchDate = Date.now(); // Use current date for active variations
    const variations =
      await serviceRateVariationsStore.getServiceRateVariationsByClient(
        props.clientId,
        searchDate,
      );
    rateVariations.value = variations || [];
  } catch (error) {
    console.error('Error loading rate variations:', error);
    rateVariations.value = [];
  } finally {
    isLoadingVariations.value = false;
  }
}

// Load service rates and rate variations when component mounts
onMounted(async () => {
  await Promise.all([loadServiceRate(), loadRateVariations()]);
});
</script>

<style scoped lang="scss">
.service-rate-table-read-only {
  .rate-text {
    font-weight: bold;
    cursor: pointer;

    // &.neutral {
    //   color: var(--text-color);
    // }

    // &.positive {
    //   color: $success;
    // }
    // &.negative {
    //   color: $error;
    // }
  }
}

.positive {
  border-color: $success;
}
.negative {
  border-color: $error;
}
</style>
